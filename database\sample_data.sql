-- Sample data for Supermarket POS
USE supermarket_pos;

-- Sample products
INSERT INTO products (name, barcode, category_id, brand_id, price, cost_price, stock, tax_percent, discount_percent, description) VALUES 
-- Groceries
('Basmati Rice 1kg', '1234567890123', 1, 1, 85.00, 70.00, 50, 5.00, 0.00, 'Premium basmati rice'),
('Wheat Flour 1kg', '1234567890124', 1, 1, 45.00, 35.00, 75, 5.00, 0.00, 'Whole wheat flour'),
('Sugar 1kg', '1234567890125', 1, 1, 42.00, 35.00, 60, 5.00, 0.00, 'White sugar'),
('Cooking Oil 1L', '1234567890126', 1, 1, 120.00, 100.00, 40, 5.00, 0.00, 'Refined cooking oil'),
('Toor Dal 1kg', '1234567890127', 1, 1, 95.00, 80.00, 35, 5.00, 0.00, 'Yellow lentils'),

-- Beverages
('Coca Cola 500ml', '2234567890123', 2, 5, 25.00, 18.00, 100, 12.00, 0.00, 'Carbonated soft drink'),
('Pepsi 500ml', '2234567890124', 2, 6, 25.00, 18.00, 95, 12.00, 0.00, 'Carbonated soft drink'),
('Mineral Water 1L', '2234567890125', 2, 1, 20.00, 15.00, 150, 18.00, 0.00, 'Packaged drinking water'),
('Orange Juice 1L', '2234567890126', 2, 7, 65.00, 50.00, 30, 12.00, 0.00, 'Fresh orange juice'),
('Tea Powder 250g', '2234567890127', 2, 1, 85.00, 70.00, 45, 5.00, 0.00, 'Black tea powder'),

-- Snacks
('Parle-G Biscuits', '3234567890123', 3, 4, 15.00, 12.00, 80, 12.00, 0.00, 'Glucose biscuits'),
('Britannia Good Day', '3234567890124', 3, 3, 25.00, 20.00, 60, 12.00, 0.00, 'Butter cookies'),
('Lays Chips 50g', '3234567890125', 3, 1, 20.00, 15.00, 70, 12.00, 0.00, 'Potato chips'),
('Kurkure 50g', '3234567890126', 3, 1, 20.00, 15.00, 65, 12.00, 0.00, 'Corn puffs'),
('Dairy Milk Chocolate', '3234567890127', 3, 1, 45.00, 35.00, 40, 12.00, 0.00, 'Milk chocolate'),

-- Personal Care
('Colgate Toothpaste', '*************', 4, 8, 85.00, 65.00, 25, 18.00, 0.00, 'Dental care'),
('Dove Soap 100g', '4234567890124', 4, 8, 45.00, 35.00, 35, 18.00, 0.00, 'Beauty soap'),
('Head & Shoulders Shampoo', '4234567890125', 4, 8, 165.00, 130.00, 20, 18.00, 0.00, 'Anti-dandruff shampoo'),
('Dettol Hand Wash', '4234567890126', 4, 8, 75.00, 60.00, 30, 18.00, 0.00, 'Antibacterial hand wash'),
('Johnson Baby Powder', '4234567890127', 4, 1, 95.00, 75.00, 15, 18.00, 0.00, 'Baby care powder'),

-- Household
('Surf Excel 1kg', '5234567890123', 5, 8, 185.00, 150.00, 25, 18.00, 0.00, 'Laundry detergent'),
('Vim Dishwash 500ml', '5234567890124', 5, 8, 65.00, 50.00, 40, 18.00, 0.00, 'Dishwashing liquid'),
('Harpic Toilet Cleaner', '*************', 5, 8, 85.00, 65.00, 30, 18.00, 0.00, 'Toilet bowl cleaner'),
('Colin Glass Cleaner', '*************', 5, 8, 75.00, 60.00, 20, 18.00, 0.00, 'Glass and surface cleaner'),
('All Out Mosquito Coil', '*************', 5, 1, 25.00, 20.00, 50, 18.00, 0.00, 'Mosquito repellent'),

-- Electronics
('Mobile Charger', '*************', 6, 1, 250.00, 200.00, 15, 18.00, 0.00, 'Universal mobile charger'),
('Earphones', '*************', 6, 1, 150.00, 120.00, 20, 18.00, 0.00, 'Wired earphones'),
('Power Bank 10000mAh', '*************', 6, 1, 1200.00, 1000.00, 8, 18.00, 0.00, 'Portable power bank'),
('USB Cable', '*************', 6, 1, 85.00, 70.00, 25, 18.00, 0.00, 'USB data cable'),
('Memory Card 32GB', '*************', 6, 1, 450.00, 380.00, 12, 18.00, 0.00, 'MicroSD card'),

-- Stationery
('A4 Paper 500 sheets', '*************', 7, 1, 285.00, 250.00, 20, 12.00, 0.00, 'Copier paper'),
('Ball Pen Blue', '*************', 7, 1, 10.00, 8.00, 100, 12.00, 0.00, 'Blue ink pen'),
('Pencil HB', '*************', 7, 1, 5.00, 4.00, 150, 12.00, 0.00, 'Writing pencil'),
('Eraser', '*************', 7, 1, 5.00, 4.00, 80, 12.00, 0.00, 'Rubber eraser'),
('Notebook 200 pages', '*************', 7, 1, 45.00, 35.00, 40, 12.00, 0.00, 'Ruled notebook'),

-- Dairy Products (Amul brand)
('Amul Milk 1L', '*************', 1, 2, 55.00, 45.00, 30, 5.00, 0.00, 'Full cream milk'),
('Amul Butter 100g', '*************', 1, 2, 65.00, 55.00, 25, 5.00, 0.00, 'Fresh butter'),
('Amul Cheese 200g', '*************', 1, 2, 125.00, 105.00, 20, 5.00, 0.00, 'Processed cheese'),
('Amul Curd 400g', '*************', 1, 2, 35.00, 28.00, 35, 5.00, 0.00, 'Fresh curd'),
('Amul Ice Cream 1L', '8234567890127', 1, 2, 185.00, 155.00, 15, 5.00, 0.00, 'Vanilla ice cream');

-- Update some products to have low stock for testing alerts
UPDATE products SET stock = 5 WHERE id IN (1, 5, 15, 25);
UPDATE products SET stock = 8 WHERE id IN (10, 20, 30);

-- Insert some sample sales for testing reports
INSERT INTO sales (invoice_no, customer_name, subtotal, tax_amount, discount_amount, total_amount, payment_method, amount_paid, change_amount, cashier_id, sale_date) VALUES 
('INV20241201001', 'John Doe', 150.00, 18.00, 0.00, 168.00, 'cash', 200.00, 32.00, 2, '2024-12-01 10:30:00'),
('INV20241201002', 'Jane Smith', 85.00, 10.20, 5.00, 90.20, 'card', 90.20, 0.00, 2, '2024-12-01 14:15:00'),
('INV20241201003', 'Bob Johnson', 245.00, 29.40, 0.00, 274.40, 'upi', 274.40, 0.00, 2, '2024-12-01 16:45:00');

-- Insert corresponding sale items
INSERT INTO sale_items (sale_id, product_id, product_name, product_barcode, quantity, unit_price, tax_percent, discount_percent, line_total) VALUES 
-- Sale 1 items
(1, 1, 'Basmati Rice 1kg', '1234567890123', 2, 85.00, 5.00, 0.00, 170.00),
(1, 6, 'Coca Cola 500ml', '2234567890123', 3, 25.00, 12.00, 0.00, 75.00),
-- Sale 2 items  
(2, 11, 'Parle-G Biscuits', '3234567890123', 2, 15.00, 12.00, 0.00, 30.00),
(2, 8, 'Mineral Water 1L', '2234567890125', 1, 20.00, 18.00, 0.00, 20.00),
(2, 2, 'Wheat Flour 1kg', '1234567890124', 1, 45.00, 5.00, 10.00, 40.50),
-- Sale 3 items
(3, 21, 'Surf Excel 1kg', '5234567890123', 1, 185.00, 18.00, 0.00, 185.00),
(3, 16, 'Colgate Toothpaste', '*************', 1, 85.00, 18.00, 0.00, 85.00),
(3, 26, 'Mobile Charger', '*************', 1, 250.00, 18.00, 0.00, 250.00);
