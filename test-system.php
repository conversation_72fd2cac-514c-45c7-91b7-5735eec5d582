<?php
// System Test Script
// This script tests basic functionality of the POS system

require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h1>Supermarket POS System Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
</style>";

$tests_passed = 0;
$tests_failed = 0;

function testResult($test_name, $result, $message = '') {
    global $tests_passed, $tests_failed;
    
    if ($result) {
        echo "<p class='success'>✓ $test_name: PASSED</p>";
        $tests_passed++;
    } else {
        echo "<p class='error'>✗ $test_name: FAILED - $message</p>";
        $tests_failed++;
    }
}

echo "<div class='test-section'>";
echo "<h2>1. Database Connection Test</h2>";

try {
    $pdo = getDBConnection();
    testResult("Database Connection", true);
} catch (Exception $e) {
    testResult("Database Connection", false, $e->getMessage());
    echo "<p class='error'>Cannot proceed with other tests without database connection.</p>";
    exit;
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>2. Database Tables Test</h2>";

$required_tables = ['users', 'products', 'categories', 'brands', 'sales', 'sale_items', 'stock_movements', 'settings'];

foreach ($required_tables as $table) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        testResult("Table '$table' exists", true, "Records: $count");
    } catch (Exception $e) {
        testResult("Table '$table' exists", false, $e->getMessage());
    }
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>3. Sample Data Test</h2>";

// Check for admin user
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE role = 'admin'");
    $stmt->execute();
    $admin_count = $stmt->fetchColumn();
    testResult("Admin users exist", $admin_count > 0, "Found $admin_count admin users");
} catch (Exception $e) {
    testResult("Admin users exist", false, $e->getMessage());
}

// Check for products
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE is_active = 1");
    $stmt->execute();
    $product_count = $stmt->fetchColumn();
    testResult("Active products exist", $product_count > 0, "Found $product_count active products");
} catch (Exception $e) {
    testResult("Active products exist", false, $e->getMessage());
}

// Check for categories
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories");
    $stmt->execute();
    $category_count = $stmt->fetchColumn();
    testResult("Categories exist", $category_count > 0, "Found $category_count categories");
} catch (Exception $e) {
    testResult("Categories exist", false, $e->getMessage());
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>4. Function Tests</h2>";

// Test utility functions
testResult("formatCurrency function", function_exists('formatCurrency'));
testResult("generateInvoiceNumber function", function_exists('generateInvoiceNumber'));
testResult("calculateTax function", function_exists('calculateTax'));
testResult("calculateDiscount function", function_exists('calculateDiscount'));

// Test function outputs
$test_amount = 100;
$tax_result = calculateTax($test_amount, 18);
testResult("Tax calculation (18% of 100)", $tax_result == 18, "Expected: 18, Got: $tax_result");

$discount_result = calculateDiscount($test_amount, 10);
testResult("Discount calculation (10% of 100)", $discount_result == 10, "Expected: 10, Got: $discount_result");

$currency_result = formatCurrency(1234.56);
testResult("Currency formatting", strpos($currency_result, '1,234.56') !== false, "Result: $currency_result");

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>5. File Structure Test</h2>";

$required_files = [
    'login.php',
    'admin/index.php',
    'admin/products.php',
    'admin/reports.php',
    'billing/index.php',
    'billing/print-invoice.php',
    'assets/css/style.css',
    'assets/js/main.js',
    'assets/js/billing.js',
    'includes/header.php',
    'includes/footer.php'
];

foreach ($required_files as $file) {
    testResult("File '$file' exists", file_exists($file));
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>6. Configuration Test</h2>";

// Test session configuration
session_start();
testResult("Session support", isset($_SESSION) || session_status() === PHP_SESSION_ACTIVE);

// Test database configuration
$config_file = 'config/database.php';
testResult("Database config file exists", file_exists($config_file));

// Test if constants are defined
if (file_exists($config_file)) {
    include_once $config_file;
    // The constants should be defined in the getDBConnection function
    testResult("Database configuration loaded", function_exists('getDBConnection'));
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Test Summary</h2>";
echo "<p class='info'><strong>Tests Passed:</strong> $tests_passed</p>";
echo "<p class='info'><strong>Tests Failed:</strong> $tests_failed</p>";
echo "<p class='info'><strong>Total Tests:</strong> " . ($tests_passed + $tests_failed) . "</p>";

if ($tests_failed == 0) {
    echo "<p class='success'><strong>🎉 All tests passed! Your POS system is ready to use.</strong></p>";
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a></p>";
} else {
    echo "<p class='error'><strong>⚠️ Some tests failed. Please check the issues above before using the system.</strong></p>";
}

echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Quick Links</h2>";
echo "<ul>";
echo "<li><a href='login.php'>Login Page</a></li>";
echo "<li><a href='admin/index.php'>Admin Dashboard</a> (Login required)</li>";
echo "<li><a href='billing/index.php'>Billing System</a> (Login required)</li>";
echo "<li><a href='README.md'>Documentation</a></li>";
echo "</ul>";

echo "<h3>Demo Credentials</h3>";
echo "<p><strong>Admin:</strong> Username: admin, Password: admin123</p>";
echo "<p><strong>Cashier:</strong> Username: cashier, Password: cashier123</p>";
echo "</div>";
?>
