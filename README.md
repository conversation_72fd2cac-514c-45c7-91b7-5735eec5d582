# Supermarket POS Billing System

A comprehensive Point of Sale (POS) billing system built with PHP, MySQL, and Bootstrap. Features include product management, billing, invoice generation, and thermal receipt printing.

## Features

### 🔐 Authentication & User Management
- Role-based access control (Admin/Cashier)
- Secure login with session management
- User profile management

### 📦 Product Management
- Add, edit, delete products
- Category and brand management
- Barcode generation and scanning support
- Stock management with low stock alerts
- Bulk import/export capabilities

### 💰 Billing System
- Real-time product search
- Shopping cart functionality
- Multiple payment methods (Cash, Card, UPI)
- Tax and discount calculations
- Customer information management

### 🧾 Invoice & Printing
- Professional invoice generation
- Thermal receipt printing (300px width)
- Print-friendly layouts
- Invoice history and reprinting

### 📊 Reports & Analytics
- Sales reports (daily, monthly, custom range)
- Product performance analysis
- Cashier performance tracking
- Stock movement reports

## Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5.3
- **Development**: XAMPP/WAMP
- **Libraries**: jQuery, Font Awesome

## Installation

### Prerequisites
- XAMPP/WAMP server
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web browser (Chrome, Firefox, Safari)

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   git clone [repository-url]
   # OR download and extract ZIP file
   ```

2. **Move to web server directory**
   ```bash
   # For XAMPP
   mv supermarket-pos /xampp/htdocs/
   
   # For WAMP
   mv supermarket-pos /wamp64/www/
   ```

3. **Start your web server**
   - Start Apache and MySQL services in XAMPP/WAMP

4. **Create Database**
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Create a new database named `supermarket_pos`
   - Import the database schema:
     ```sql
     -- Run the contents of database/schema.sql
     -- Then run database/sample_data.sql for test data
     ```

5. **Configure Database Connection**
   - Edit `config/database.php` if needed
   - Default settings work with standard XAMPP/WAMP setup

6. **Access the Application**
   - Open browser and go to: `http://localhost/supermarket-pos`
   - Use demo credentials to login

## Demo Credentials

### Admin Account
- **Username**: admin
- **Password**: admin123

### Cashier Account
- **Username**: cashier
- **Password**: cashier123

## File Structure

```
supermarket-pos/
├── admin/                  # Admin panel pages
│   ├── index.php          # Dashboard
│   ├── products.php       # Product management
│   └── reports.php        # Reports & analytics
├── assets/                # Static assets
│   ├── css/
│   │   └── style.css      # Custom styles
│   └── js/
│       ├── main.js        # Common JavaScript
│       ├── billing.js     # Billing functionality
│       └── products.js    # Product management
├── billing/               # Billing system
│   ├── index.php          # Main billing interface
│   ├── print-invoice.php  # Invoice printing
│   └── ajax/              # AJAX endpoints
├── config/                # Configuration files
│   ├── database.php       # Database connection
│   └── session.php        # Session management
├── database/              # Database files
│   ├── schema.sql         # Database structure
│   └── sample_data.sql    # Sample data
├── includes/              # Common includes
│   ├── header.php         # Common header
│   ├── footer.php         # Common footer
│   └── functions.php      # Utility functions
├── login.php              # Login page
└── index.php              # Main entry point
```

## Usage Guide

### For Cashiers
1. **Login** with cashier credentials
2. **Search Products** by name or scan barcode
3. **Add to Cart** with desired quantities
4. **Checkout** with customer details and payment method
5. **Print Invoice** automatically or manually

### For Admins
1. **Dashboard** - View sales overview and alerts
2. **Product Management** - Add/edit products, categories, brands
3. **Reports** - Generate various sales and performance reports
4. **User Management** - Manage cashier accounts

## Key Features Explained

### Barcode Support
- Auto-generation of barcodes for new products
- Barcode scanning support (with compatible scanners)
- Search by barcode in billing system

### Thermal Receipt Printing
- Optimized for 300px width thermal printers
- Uses `window.print()` for browser-based printing
- Professional receipt layout with store branding

### Stock Management
- Real-time stock updates during sales
- Low stock alerts and notifications
- Stock movement tracking

### Tax & Discount System
- Product-level tax and discount configuration
- Automatic calculations during billing
- Detailed breakdown in invoices

## Customization

### Store Settings
Edit the settings in the database `settings` table:
- Store name, address, phone
- GST number
- Receipt footer text

### Styling
- Modify `assets/css/style.css` for custom styling
- Bootstrap classes can be overridden
- Thermal receipt styling in print-invoice.php

### Adding Features
- Follow the existing code structure
- Use PDO for database operations
- Implement proper session checks
- Add appropriate error handling

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check XAMPP/WAMP services are running
   - Verify database credentials in config/database.php
   - Ensure database exists and is properly imported

2. **Login Issues**
   - Clear browser cache and cookies
   - Check if sample data was imported correctly
   - Verify user credentials in database

3. **Printing Issues**
   - Ensure browser allows printing
   - Check printer settings for thermal printers
   - Test with different browsers

4. **Search Not Working**
   - Check if jQuery is loading properly
   - Verify AJAX endpoints are accessible
   - Check browser console for JavaScript errors

## Security Features

- Password hashing with PHP's password_hash()
- SQL injection prevention with prepared statements
- XSS protection with input sanitization
- Session-based authentication
- Role-based access control

## Browser Compatibility

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## License

This project is open source and available under the [MIT License](LICENSE).

## Support

For support and questions:
- Check the troubleshooting section
- Review the code comments
- Test with the provided sample data

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Note**: This system is designed for small to medium-sized retail businesses. For enterprise-level requirements, additional features and optimizations may be needed.
