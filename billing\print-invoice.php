<?php
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

requireLogin();

$pdo = getDBConnection();

$sale_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$sale_id) {
    header('Location: index.php');
    exit;
}

// Get sale details
$stmt = $pdo->prepare("
    SELECT s.*, u.full_name as cashier_name 
    FROM sales s 
    JOIN users u ON s.cashier_id = u.id 
    WHERE s.id = ?
");
$stmt->execute([$sale_id]);
$sale = $stmt->fetch();

if (!$sale) {
    header('Location: index.php');
    exit;
}

// Get sale items
$stmt = $pdo->prepare("
    SELECT * FROM sale_items 
    WHERE sale_id = ? 
    ORDER BY id
");
$stmt->execute([$sale_id]);
$sale_items = $stmt->fetchAll();

// Get store settings
$stmt = $pdo->prepare("SELECT * FROM settings WHERE setting_key IN ('store_name', 'store_address', 'store_phone', 'store_email', 'store_gst')");
$stmt->execute();
$settings_data = $stmt->fetchAll();

$settings = [];
foreach ($settings_data as $setting) {
    $settings[$setting['setting_key']] = $setting['setting_value'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<?php echo $sale['invoice_no']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
            .thermal-receipt { 
                width: 300px !important; 
                margin: 0 !important;
                box-shadow: none !important;
            }
        }
        
        .thermal-receipt {
            width: 300px;
            margin: 20px auto;
            padding: 15px;
            border: 1px solid #ddd;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .thermal-receipt .header {
            text-align: center;
            border-bottom: 1px dashed #333;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .thermal-receipt .store-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .thermal-receipt .store-info {
            font-size: 10px;
            margin-bottom: 2px;
        }
        
        .thermal-receipt .invoice-info {
            margin: 10px 0;
            font-size: 11px;
        }
        
        .thermal-receipt .items-table {
            width: 100%;
            margin: 10px 0;
        }
        
        .thermal-receipt .items-table th,
        .thermal-receipt .items-table td {
            padding: 2px 0;
            font-size: 11px;
        }
        
        .thermal-receipt .items-table th {
            border-bottom: 1px solid #333;
            font-weight: bold;
        }
        
        .thermal-receipt .total-section {
            border-top: 1px dashed #333;
            padding-top: 10px;
            margin-top: 10px;
        }
        
        .thermal-receipt .total-line {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
        }
        
        .thermal-receipt .grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 1px solid #333;
            padding-top: 5px;
            margin-top: 5px;
        }
        
        .thermal-receipt .footer {
            text-align: center;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px dashed #333;
            font-size: 10px;
        }
        
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .text-left { text-align: left; }
    </style>
</head>
<body>
    <!-- Print Controls -->
    <div class="container-fluid no-print">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center py-3">
                    <h4>Invoice Preview</h4>
                    <div>
                        <button type="button" class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> Print Invoice
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Billing
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Thermal Receipt -->
    <div class="thermal-receipt">
        <!-- Header -->
        <div class="header">
            <div class="store-name"><?php echo strtoupper($settings['store_name'] ?? 'SUPERMARKET POS'); ?></div>
            <?php if (!empty($settings['store_address'])): ?>
                <div class="store-info"><?php echo $settings['store_address']; ?></div>
            <?php endif; ?>
            <?php if (!empty($settings['store_phone'])): ?>
                <div class="store-info">Phone: <?php echo $settings['store_phone']; ?></div>
            <?php endif; ?>
            <?php if (!empty($settings['store_email'])): ?>
                <div class="store-info">Email: <?php echo $settings['store_email']; ?></div>
            <?php endif; ?>
            <?php if (!empty($settings['store_gst'])): ?>
                <div class="store-info">GST: <?php echo $settings['store_gst']; ?></div>
            <?php endif; ?>
        </div>

        <!-- Invoice Info -->
        <div class="invoice-info">
            <div style="display: flex; justify-content: space-between;">
                <span>Invoice: <?php echo $sale['invoice_no']; ?></span>
                <span><?php echo date('d/m/Y H:i', strtotime($sale['sale_date'])); ?></span>
            </div>
            <?php if ($sale['customer_name']): ?>
                <div>Customer: <?php echo $sale['customer_name']; ?></div>
            <?php endif; ?>
            <?php if ($sale['customer_phone']): ?>
                <div>Phone: <?php echo $sale['customer_phone']; ?></div>
            <?php endif; ?>
            <div>Cashier: <?php echo $sale['cashier_name']; ?></div>
        </div>

        <!-- Items -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="text-align: left;">Item</th>
                    <th style="text-align: center;">Qty</th>
                    <th style="text-align: right;">Rate</th>
                    <th style="text-align: right;">Amount</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($sale_items as $item): ?>
                    <tr>
                        <td style="text-align: left;">
                            <?php echo htmlspecialchars($item['product_name']); ?>
                            <?php if ($item['tax_percent'] > 0 || $item['discount_percent'] > 0): ?>
                                <br>
                                <small style="font-size: 9px;">
                                    <?php if ($item['tax_percent'] > 0): ?>
                                        Tax: <?php echo $item['tax_percent']; ?>%
                                    <?php endif; ?>
                                    <?php if ($item['discount_percent'] > 0): ?>
                                        Disc: <?php echo $item['discount_percent']; ?>%
                                    <?php endif; ?>
                                </small>
                            <?php endif; ?>
                        </td>
                        <td style="text-align: center;"><?php echo $item['quantity']; ?></td>
                        <td style="text-align: right;"><?php echo number_format($item['unit_price'], 2); ?></td>
                        <td style="text-align: right;"><?php echo number_format($item['line_total'], 2); ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <!-- Totals -->
        <div class="total-section">
            <div class="total-line">
                <span>Subtotal:</span>
                <span>₹<?php echo number_format($sale['subtotal'], 2); ?></span>
            </div>
            
            <?php if ($sale['tax_amount'] > 0): ?>
                <div class="total-line">
                    <span>Tax:</span>
                    <span>₹<?php echo number_format($sale['tax_amount'], 2); ?></span>
                </div>
            <?php endif; ?>
            
            <?php if ($sale['discount_amount'] > 0): ?>
                <div class="total-line">
                    <span>Discount:</span>
                    <span>-₹<?php echo number_format($sale['discount_amount'], 2); ?></span>
                </div>
            <?php endif; ?>
            
            <div class="total-line grand-total">
                <span>TOTAL:</span>
                <span>₹<?php echo number_format($sale['total_amount'], 2); ?></span>
            </div>
            
            <div class="total-line">
                <span>Paid (<?php echo ucfirst($sale['payment_method']); ?>):</span>
                <span>₹<?php echo number_format($sale['amount_paid'], 2); ?></span>
            </div>
            
            <?php if ($sale['change_amount'] > 0): ?>
                <div class="total-line">
                    <span>Change:</span>
                    <span>₹<?php echo number_format($sale['change_amount'], 2); ?></span>
                </div>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div>*** THANK YOU FOR SHOPPING ***</div>
            <div>Visit Again!</div>
            <div style="margin-top: 10px; font-size: 9px;">
                Powered by Supermarket POS System
            </div>
        </div>
    </div>

    <!-- Auto-print script -->
    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() {
        //     window.print();
        // };
        
        // Print function
        function printInvoice() {
            window.print();
        }
        
        // Keyboard shortcut for printing
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
        });
    </script>
</body>
</html>
