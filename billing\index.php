<?php
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

requireLogin();

$pdo = getDBConnection();

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

$message = '';

// Handle cart actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_to_cart':
                $product_id = intval($_POST['product_id']);
                $quantity = intval($_POST['quantity']);
                
                // Get product details
                $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ? AND is_active = 1");
                $stmt->execute([$product_id]);
                $product = $stmt->fetch();
                
                if ($product) {
                    if ($product['stock'] >= $quantity) {
                        // Add to cart
                        if (isset($_SESSION['cart'][$product_id])) {
                            $_SESSION['cart'][$product_id]['quantity'] += $quantity;
                        } else {
                            $_SESSION['cart'][$product_id] = [
                                'id' => $product['id'],
                                'name' => $product['name'],
                                'barcode' => $product['barcode'],
                                'price' => $product['price'],
                                'tax_percent' => $product['tax_percent'],
                                'discount_percent' => $product['discount_percent'],
                                'quantity' => $quantity,
                                'stock' => $product['stock']
                            ];
                        }
                        
                        // Check if quantity exceeds stock
                        if ($_SESSION['cart'][$product_id]['quantity'] > $product['stock']) {
                            $_SESSION['cart'][$product_id]['quantity'] = $product['stock'];
                            $message = showAlert('Quantity adjusted to available stock (' . $product['stock'] . ')', 'warning');
                        } else {
                            $message = showAlert('Product added to cart!', 'success');
                        }
                    } else {
                        $message = showAlert('Insufficient stock! Available: ' . $product['stock'], 'danger');
                    }
                } else {
                    $message = showAlert('Product not found!', 'danger');
                }
                break;
                
            case 'update_cart':
                $product_id = intval($_POST['product_id']);
                $quantity = intval($_POST['quantity']);
                
                if (isset($_SESSION['cart'][$product_id])) {
                    if ($quantity <= 0) {
                        unset($_SESSION['cart'][$product_id]);
                        $message = showAlert('Product removed from cart!', 'info');
                    } else {
                        // Check stock
                        if ($quantity <= $_SESSION['cart'][$product_id]['stock']) {
                            $_SESSION['cart'][$product_id]['quantity'] = $quantity;
                            $message = showAlert('Cart updated!', 'success');
                        } else {
                            $message = showAlert('Quantity exceeds available stock!', 'danger');
                        }
                    }
                }
                break;
                
            case 'remove_from_cart':
                $product_id = intval($_POST['product_id']);
                if (isset($_SESSION['cart'][$product_id])) {
                    unset($_SESSION['cart'][$product_id]);
                    $message = showAlert('Product removed from cart!', 'info');
                }
                break;
                
            case 'clear_cart':
                $_SESSION['cart'] = [];
                $message = showAlert('Cart cleared!', 'info');
                break;
                
            case 'generate_invoice':
                if (!empty($_SESSION['cart'])) {
                    // Calculate totals
                    $subtotal = 0;
                    $total_tax = 0;
                    $total_discount = 0;
                    
                    foreach ($_SESSION['cart'] as $item) {
                        $line_total = $item['price'] * $item['quantity'];
                        $tax_amount = calculateTax($line_total, $item['tax_percent']);
                        $discount_amount = calculateDiscount($line_total, $item['discount_percent']);
                        
                        $subtotal += $line_total;
                        $total_tax += $tax_amount;
                        $total_discount += $discount_amount;
                    }
                    
                    $total_amount = $subtotal + $total_tax - $total_discount;
                    
                    // Get form data
                    $customer_name = sanitizeInput($_POST['customer_name']);
                    $customer_phone = sanitizeInput($_POST['customer_phone']);
                    $payment_method = $_POST['payment_method'];
                    $amount_paid = floatval($_POST['amount_paid']);
                    $change_amount = $amount_paid - $total_amount;
                    
                    if ($amount_paid < $total_amount) {
                        $message = showAlert('Insufficient payment amount!', 'danger');
                    } else {
                        try {
                            $pdo->beginTransaction();
                            
                            // Generate invoice number
                            $invoice_no = generateInvoiceNumber();
                            
                            // Insert sale
                            $stmt = $pdo->prepare("INSERT INTO sales (invoice_no, customer_name, customer_phone, subtotal, tax_amount, discount_amount, total_amount, payment_method, amount_paid, change_amount, cashier_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                            $stmt->execute([$invoice_no, $customer_name, $customer_phone, $subtotal, $total_tax, $total_discount, $total_amount, $payment_method, $amount_paid, $change_amount, $_SESSION['user_id']]);
                            
                            $sale_id = $pdo->lastInsertId();
                            
                            // Insert sale items and update stock
                            foreach ($_SESSION['cart'] as $item) {
                                $line_total = $item['price'] * $item['quantity'];
                                
                                // Insert sale item
                                $stmt = $pdo->prepare("INSERT INTO sale_items (sale_id, product_id, product_name, product_barcode, quantity, unit_price, tax_percent, discount_percent, line_total) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                                $stmt->execute([$sale_id, $item['id'], $item['name'], $item['barcode'], $item['quantity'], $item['price'], $item['tax_percent'], $item['discount_percent'], $line_total]);
                                
                                // Update product stock
                                $stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ?");
                                $stmt->execute([$item['quantity'], $item['id']]);
                                
                                // Insert stock movement
                                $stmt = $pdo->prepare("INSERT INTO stock_movements (product_id, movement_type, quantity, reference_type, reference_id, created_by) VALUES (?, 'out', ?, 'sale', ?, ?)");
                                $stmt->execute([$item['id'], $item['quantity'], $sale_id, $_SESSION['user_id']]);
                            }
                            
                            $pdo->commit();
                            
                            // Clear cart
                            $_SESSION['cart'] = [];
                            
                            // Redirect to print invoice
                            header("Location: print-invoice.php?id=$sale_id");
                            exit();
                            
                        } catch (Exception $e) {
                            $pdo->rollBack();
                            $message = showAlert('Error generating invoice: ' . $e->getMessage(), 'danger');
                        }
                    }
                } else {
                    $message = showAlert('Cart is empty!', 'warning');
                }
                break;
        }
    }
}

// Calculate cart totals
$cart_subtotal = 0;
$cart_tax = 0;
$cart_discount = 0;
$cart_total = 0;

foreach ($_SESSION['cart'] as $item) {
    $line_total = $item['price'] * $item['quantity'];
    $tax_amount = calculateTax($line_total, $item['tax_percent']);
    $discount_amount = calculateDiscount($line_total, $item['discount_percent']);
    
    $cart_subtotal += $line_total;
    $cart_tax += $tax_amount;
    $cart_discount += $discount_amount;
}

$cart_total = $cart_subtotal + $cart_tax - $cart_discount;

$page_title = 'Billing System';
$extra_js = ['/assets/js/billing.js'];
include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-cash-register"></i> Billing System
            <small class="text-muted">Welcome, <?php echo getCurrentUser()['username']; ?></small>
        </h2>
    </div>
</div>

<?php echo $message; ?>

<div class="row">
    <!-- Product Search -->
    <div class="col-lg-8">
        <div class="billing-container">
            <h4><i class="fas fa-search"></i> Product Search</h4>
            
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="position-relative">
                        <input type="text" class="form-control form-control-lg" id="productSearch" 
                               placeholder="Search by product name or scan barcode..." autofocus>
                        <div id="searchResults" class="search-results" style="display: none;"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-outline-secondary btn-lg w-100" id="clearSearch">
                        <i class="fas fa-times"></i> Clear
                    </button>
                </div>
            </div>
            
            <!-- Quick Add Form -->
            <div class="card mb-4" id="quickAddForm" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">Add to Cart</h5>
                </div>
                <div class="card-body">
                    <form id="addToCartForm">
                        <input type="hidden" id="selectedProductId" name="product_id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Product</label>
                                    <div id="selectedProductInfo"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">Quantity</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" 
                                           min="1" value="1" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-plus"></i> Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cart -->
    <div class="col-lg-4">
        <div class="billing-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4><i class="fas fa-shopping-cart"></i> Cart</h4>
                <?php if (!empty($_SESSION['cart'])): ?>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearCart()">
                        <i class="fas fa-trash"></i> Clear
                    </button>
                <?php endif; ?>
            </div>
            
            <div id="cartItems">
                <?php if (empty($_SESSION['cart'])): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>Cart is empty</p>
                        <small>Search and add products to start billing</small>
                    </div>
                <?php else: ?>
                    <?php foreach ($_SESSION['cart'] as $product_id => $item): ?>
                        <div class="cart-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($item['name']); ?></h6>
                                    <small class="text-muted"><?php echo $item['barcode']; ?></small>
                                    <div class="mt-1">
                                        <span class="badge bg-secondary"><?php echo formatCurrency($item['price']); ?></span>
                                        <?php if ($item['tax_percent'] > 0): ?>
                                            <span class="badge bg-info">Tax: <?php echo $item['tax_percent']; ?>%</span>
                                        <?php endif; ?>
                                        <?php if ($item['discount_percent'] > 0): ?>
                                            <span class="badge bg-warning">Disc: <?php echo $item['discount_percent']; ?>%</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                        onclick="removeFromCart(<?php echo $product_id; ?>)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            
                            <div class="row mt-2">
                                <div class="col-6">
                                    <div class="input-group input-group-sm">
                                        <button type="button" class="btn btn-outline-secondary" 
                                                onclick="updateQuantity(<?php echo $product_id; ?>, <?php echo $item['quantity'] - 1; ?>)">-</button>
                                        <input type="number" class="form-control text-center" 
                                               value="<?php echo $item['quantity']; ?>" min="1" max="<?php echo $item['stock']; ?>"
                                               onchange="updateQuantity(<?php echo $product_id; ?>, this.value)">
                                        <button type="button" class="btn btn-outline-secondary" 
                                                onclick="updateQuantity(<?php echo $product_id; ?>, <?php echo $item['quantity'] + 1; ?>)">+</button>
                                    </div>
                                </div>
                                <div class="col-6 text-end">
                                    <strong><?php echo formatCurrency($item['price'] * $item['quantity']); ?></strong>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <?php if (!empty($_SESSION['cart'])): ?>
                <!-- Cart Total -->
                <div class="cart-total">
                    <div class="d-flex justify-content-between">
                        <span>Subtotal:</span>
                        <span><?php echo formatCurrency($cart_subtotal); ?></span>
                    </div>
                    <?php if ($cart_tax > 0): ?>
                        <div class="d-flex justify-content-between">
                            <span>Tax:</span>
                            <span><?php echo formatCurrency($cart_tax); ?></span>
                        </div>
                    <?php endif; ?>
                    <?php if ($cart_discount > 0): ?>
                        <div class="d-flex justify-content-between text-success">
                            <span>Discount:</span>
                            <span>-<?php echo formatCurrency($cart_discount); ?></span>
                        </div>
                    <?php endif; ?>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Total:</strong>
                        <strong class="text-primary"><?php echo formatCurrency($cart_total); ?></strong>
                    </div>
                    
                    <button type="button" class="btn btn-success btn-lg w-100 mt-3" 
                            data-bs-toggle="modal" data-bs-target="#checkoutModal">
                        <i class="fas fa-credit-card"></i> Checkout
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Checkout Modal -->
<div class="modal fade" id="checkoutModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-credit-card"></i> Checkout</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="checkoutForm">
                <input type="hidden" name="action" value="generate_invoice">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Customer Information</h6>
                            <div class="mb-3">
                                <label for="customer_name" class="form-label">Customer Name</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name"
                                       placeholder="Walk-in Customer">
                            </div>
                            <div class="mb-3">
                                <label for="customer_phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="customer_phone" name="customer_phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Payment Information</h6>
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <option value="cash">Cash</option>
                                    <option value="card">Card</option>
                                    <option value="upi">UPI</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="amount_paid" class="form-label">Amount Paid</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="amount_paid" name="amount_paid"
                                           step="0.01" min="<?php echo $cart_total; ?>"
                                           value="<?php echo $cart_total; ?>" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Change Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="text" class="form-control" id="change_amount" readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <hr>
                    <h6>Order Summary</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Qty</th>
                                    <th>Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($_SESSION['cart'] as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td><?php echo formatCurrency($item['price']); ?></td>
                                        <td><?php echo formatCurrency($item['price'] * $item['quantity']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3">Subtotal:</th>
                                    <th><?php echo formatCurrency($cart_subtotal); ?></th>
                                </tr>
                                <?php if ($cart_tax > 0): ?>
                                    <tr>
                                        <th colspan="3">Tax:</th>
                                        <th><?php echo formatCurrency($cart_tax); ?></th>
                                    </tr>
                                <?php endif; ?>
                                <?php if ($cart_discount > 0): ?>
                                    <tr class="text-success">
                                        <th colspan="3">Discount:</th>
                                        <th>-<?php echo formatCurrency($cart_discount); ?></th>
                                    </tr>
                                <?php endif; ?>
                                <tr class="table-primary">
                                    <th colspan="3">Total:</th>
                                    <th><?php echo formatCurrency($cart_total); ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success btn-lg">
                        <i class="fas fa-print"></i> Generate Invoice
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
