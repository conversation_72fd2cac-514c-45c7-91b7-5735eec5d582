// Main JavaScript file for Supermarket POS

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Confirm delete actions
    $('.delete-btn').on('click', function(e) {
        if (!confirm('Are you sure you want to delete this item?')) {
            e.preventDefault();
        }
    });

    // Format currency inputs
    $('.currency-input').on('input', function() {
        let value = $(this).val().replace(/[^\d.]/g, '');
        $(this).val(value);
    });

    // Format number inputs
    $('.number-input').on('input', function() {
        let value = $(this).val().replace(/[^\d]/g, '');
        $(this).val(value);
    });

    // Auto-focus on first input
    $('input:visible:first').focus();
});

// Utility Functions
function formatCurrency(amount) {
    return '₹' + parseFloat(amount).toFixed(2);
}

function formatNumber(number) {
    return parseFloat(number).toFixed(2);
}

function showAlert(message, type = 'success') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at the top of the page
    $('main').prepend(alertHtml);
    
    // Auto-hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
}

// AJAX helper function
function makeAjaxRequest(url, data, successCallback, errorCallback) {
    $.ajax({
        url: url,
        type: 'POST',
        data: data,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                if (successCallback) successCallback(response);
            } else {
                if (errorCallback) {
                    errorCallback(response.message || 'An error occurred');
                } else {
                    showAlert(response.message || 'An error occurred', 'danger');
                }
            }
        },
        error: function(xhr, status, error) {
            if (errorCallback) {
                errorCallback('Network error: ' + error);
            } else {
                showAlert('Network error: ' + error, 'danger');
            }
        }
    });
}

// Print function for invoices
function printInvoice() {
    window.print();
}

// Barcode validation
function validateBarcode(barcode) {
    return /^[0-9]{8,13}$/.test(barcode);
}

// Price validation
function validatePrice(price) {
    return /^\d+(\.\d{1,2})?$/.test(price) && parseFloat(price) > 0;
}

// Stock validation
function validateStock(stock) {
    return /^\d+$/.test(stock) && parseInt(stock) >= 0;
}

// Percentage validation
function validatePercentage(percentage) {
    return /^\d+(\.\d{1,2})?$/.test(percentage) && parseFloat(percentage) >= 0 && parseFloat(percentage) <= 100;
}

// Form validation helper
function validateForm(formId) {
    let isValid = true;
    const form = $(formId);
    
    form.find('input[required], select[required], textarea[required]').each(function() {
        const field = $(this);
        const value = field.val().trim();
        
        if (!value) {
            field.addClass('is-invalid');
            isValid = false;
        } else {
            field.removeClass('is-invalid');
            field.addClass('is-valid');
        }
    });
    
    return isValid;
}

// Clear form validation
function clearFormValidation(formId) {
    $(formId).find('.is-invalid, .is-valid').removeClass('is-invalid is-valid');
}

// Loading spinner
function showLoading(element) {
    $(element).html('<i class="fas fa-spinner fa-spin"></i> Loading...');
    $(element).prop('disabled', true);
}

function hideLoading(element, originalText) {
    $(element).html(originalText);
    $(element).prop('disabled', false);
}

// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
