<?php
// Common utility functions

// Format currency
function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}

// Format date
function formatDate($date) {
    return date('d/m/Y', strtotime($date));
}

// Format datetime
function formatDateTime($datetime) {
    return date('d/m/Y H:i:s', strtotime($datetime));
}

// Generate invoice number
function generateInvoiceNumber() {
    return 'INV' . date('Ymd') . rand(1000, 9999);
}

// Calculate tax amount
function calculateTax($amount, $tax_percent) {
    return ($amount * $tax_percent) / 100;
}

// Calculate discount amount
function calculateDiscount($amount, $discount_percent) {
    return ($amount * $discount_percent) / 100;
}

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Show alert message
function showAlert($message, $type = 'success') {
    return '<div class="alert alert-' . $type . ' alert-dismissible fade show" role="alert">
                ' . $message . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
}

// Check if product is low stock
function isLowStock($stock, $threshold = 10) {
    return $stock <= $threshold;
}

// Validate barcode format
function validateBarcode($barcode) {
    return preg_match('/^[0-9]{8,13}$/', $barcode);
}

// Generate barcode if not provided
function generateBarcode() {
    return str_pad(rand(1, 999999999999), 12, '0', STR_PAD_LEFT);
}

// Validate price
function validatePrice($price) {
    return is_numeric($price) && $price > 0;
}

// Validate stock
function validateStock($stock) {
    return is_numeric($stock) && $stock >= 0;
}

// Get current user details
function getCurrentUser() {
    if (isset($_SESSION['user_id'])) {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    }
    return null;
}
?>
