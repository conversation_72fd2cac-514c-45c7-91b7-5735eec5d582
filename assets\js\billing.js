// Billing system JavaScript

$(document).ready(function() {
    let searchTimeout;
    
    // Product search functionality
    $('#productSearch').on('input', function() {
        clearTimeout(searchTimeout);
        const query = $(this).val().trim();
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(function() {
                searchProducts(query);
            }, 300);
        } else {
            $('#searchResults').hide();
        }
    });
    
    // Handle barcode scanner input
    $('#productSearch').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            const query = $(this).val().trim();
            if (query.length > 0) {
                searchProducts(query, true); // Direct search for barcode
            }
        }
    });
    
    // Clear search
    $('#clearSearch').on('click', function() {
        $('#productSearch').val('');
        $('#searchResults').hide();
        $('#quickAddForm').hide();
    });
    
    // Add to cart form submission
    $('#addToCartForm').on('submit', function(e) {
        e.preventDefault();
        
        const productId = $('#selectedProductId').val();
        const quantity = $('#quantity').val();
        
        if (productId && quantity > 0) {
            addToCart(productId, quantity);
        }
    });
    
    // Calculate change amount
    $('#amount_paid').on('input', function() {
        const amountPaid = parseFloat($(this).val()) || 0;
        const total = parseFloat($('#checkoutModal').data('total')) || 0;
        const change = amountPaid - total;
        
        $('#change_amount').val(change >= 0 ? change.toFixed(2) : '0.00');
        
        // Update button state
        if (amountPaid >= total) {
            $('#checkoutForm button[type="submit"]').prop('disabled', false);
        } else {
            $('#checkoutForm button[type="submit"]').prop('disabled', true);
        }
    });
    
    // Set total amount in modal
    $('#checkoutModal').on('show.bs.modal', function() {
        const total = parseFloat($('.cart-total strong:last').text().replace(/[^\d.-]/g, ''));
        $(this).data('total', total);
        $('#amount_paid').trigger('input');
    });
    
    // Auto-focus on product search
    $('#productSearch').focus();
    
    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // F1 - Focus search
        if (e.key === 'F1') {
            e.preventDefault();
            $('#productSearch').focus();
        }
        
        // F2 - Clear cart
        if (e.key === 'F2') {
            e.preventDefault();
            if (confirm('Clear entire cart?')) {
                clearCart();
            }
        }
        
        // F3 - Checkout
        if (e.key === 'F3') {
            e.preventDefault();
            if ($('.cart-item').length > 0) {
                $('#checkoutModal').modal('show');
            }
        }
    });
});

// Search products function
function searchProducts(query, isBarcode = false) {
    $.ajax({
        url: 'ajax/search-products.php',
        method: 'GET',
        data: { 
            q: query,
            barcode: isBarcode ? 1 : 0
        },
        success: function(response) {
            if (response.success) {
                displaySearchResults(response.products);
                
                // If barcode search and single result, auto-select
                if (isBarcode && response.products.length === 1) {
                    selectProduct(response.products[0]);
                }
            } else {
                $('#searchResults').html('<div class="search-item text-muted">No products found</div>').show();
            }
        },
        error: function() {
            showAlert('Error searching products', 'danger');
        }
    });
}

// Display search results
function displaySearchResults(products) {
    let html = '';
    
    products.forEach(function(product) {
        const stockClass = product.stock <= product.min_stock ? 'text-warning' : 'text-success';
        const stockText = product.stock <= 0 ? 'Out of Stock' : `Stock: ${product.stock}`;
        
        html += `
            <div class="search-item" onclick="selectProduct(${JSON.stringify(product).replace(/"/g, '&quot;')})">
                <div class="d-flex justify-content-between">
                    <div>
                        <strong>${product.name}</strong>
                        <br>
                        <small class="text-muted">${product.barcode}</small>
                        <span class="badge bg-secondary ms-2">₹${product.price}</span>
                    </div>
                    <div class="text-end">
                        <small class="${stockClass}">${stockText}</small>
                        ${product.category_name ? `<br><small class="text-muted">${product.category_name}</small>` : ''}
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#searchResults').html(html).show();
}

// Select product for adding to cart
function selectProduct(product) {
    if (product.stock <= 0) {
        showAlert('Product is out of stock!', 'warning');
        return;
    }
    
    $('#selectedProductId').val(product.id);
    $('#selectedProductInfo').html(`
        <strong>${product.name}</strong><br>
        <small class="text-muted">${product.barcode}</small>
        <span class="badge bg-secondary">₹${product.price}</span>
        <span class="badge bg-info">Stock: ${product.stock}</span>
    `);
    
    $('#quantity').attr('max', product.stock).val(1);
    $('#searchResults').hide();
    $('#quickAddForm').show();
    $('#quantity').focus().select();
}

// Add product to cart
function addToCart(productId, quantity) {
    $.ajax({
        url: '',
        method: 'POST',
        data: {
            action: 'add_to_cart',
            product_id: productId,
            quantity: quantity
        },
        success: function() {
            location.reload(); // Reload to update cart
        },
        error: function() {
            showAlert('Error adding product to cart', 'danger');
        }
    });
}

// Update cart item quantity
function updateQuantity(productId, quantity) {
    if (quantity <= 0) {
        removeFromCart(productId);
        return;
    }
    
    $.ajax({
        url: '',
        method: 'POST',
        data: {
            action: 'update_cart',
            product_id: productId,
            quantity: quantity
        },
        success: function() {
            location.reload();
        },
        error: function() {
            showAlert('Error updating cart', 'danger');
        }
    });
}

// Remove item from cart
function removeFromCart(productId) {
    if (confirm('Remove this item from cart?')) {
        $.ajax({
            url: '',
            method: 'POST',
            data: {
                action: 'remove_from_cart',
                product_id: productId
            },
            success: function() {
                location.reload();
            },
            error: function() {
                showAlert('Error removing item from cart', 'danger');
            }
        });
    }
}

// Clear entire cart
function clearCart() {
    $.ajax({
        url: '',
        method: 'POST',
        data: {
            action: 'clear_cart'
        },
        success: function() {
            location.reload();
        },
        error: function() {
            showAlert('Error clearing cart', 'danger');
        }
    });
}

// Barcode scanner integration
function handleBarcodeScanned(barcode) {
    $('#productSearch').val(barcode);
    searchProducts(barcode, true);
}

// Quick quantity buttons
function quickQuantity(amount) {
    const currentQty = parseInt($('#quantity').val()) || 1;
    const newQty = Math.max(1, currentQty + amount);
    const maxQty = parseInt($('#quantity').attr('max')) || 999;
    
    $('#quantity').val(Math.min(newQty, maxQty));
}

// Payment method change handler
$('#payment_method').on('change', function() {
    const method = $(this).val();
    
    if (method === 'card' || method === 'upi') {
        // For digital payments, set exact amount
        const total = parseFloat($('#checkoutModal').data('total'));
        $('#amount_paid').val(total.toFixed(2));
    }
    
    $('#amount_paid').trigger('input');
});

// Print shortcuts
function printLastInvoice() {
    // This would be implemented to print the last generated invoice
    window.open('print-invoice.php?last=1', '_blank');
}

// Customer quick fill
function fillCustomer(name, phone) {
    $('#customer_name').val(name);
    $('#customer_phone').val(phone);
}
