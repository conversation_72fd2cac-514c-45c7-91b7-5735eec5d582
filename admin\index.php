<?php
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

requireAdmin();

$pdo = getDBConnection();

// Get dashboard statistics
$stats = [];

// Total products
$stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE is_active = 1");
$stats['total_products'] = $stmt->fetch()['count'];

// Low stock products
$stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE stock <= min_stock AND is_active = 1");
$stats['low_stock'] = $stmt->fetch()['count'];

// Today's sales
$stmt = $pdo->query("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales WHERE DATE(sale_date) = CURDATE()");
$today_sales = $stmt->fetch();
$stats['today_sales_count'] = $today_sales['count'];
$stats['today_sales_amount'] = $today_sales['total'];

// This month's sales
$stmt = $pdo->query("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales WHERE MONTH(sale_date) = MONTH(CURDATE()) AND YEAR(sale_date) = YEAR(CURDATE())");
$month_sales = $stmt->fetch();
$stats['month_sales_count'] = $month_sales['count'];
$stats['month_sales_amount'] = $month_sales['total'];

// Recent sales
$stmt = $pdo->query("SELECT s.*, u.full_name as cashier_name FROM sales s JOIN users u ON s.cashier_id = u.id ORDER BY s.sale_date DESC LIMIT 5");
$recent_sales = $stmt->fetchAll();

// Low stock products
$stmt = $pdo->query("SELECT p.*, c.name as category_name, b.name as brand_name FROM products p LEFT JOIN categories c ON p.category_id = c.id LEFT JOIN brands b ON p.brand_id = b.id WHERE p.stock <= p.min_stock AND p.is_active = 1 ORDER BY p.stock ASC LIMIT 10");
$low_stock_products = $stmt->fetchAll();

$page_title = 'Admin Dashboard';
include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Admin Dashboard
            <small class="text-muted">Welcome, <?php echo getCurrentUser()['username']; ?></small>
        </h2>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-box fa-3x mb-3"></i>
                <h3><?php echo $stats['total_products']; ?></h3>
                <p class="mb-0">Total Products</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h3><?php echo $stats['low_stock']; ?></h3>
                <p class="mb-0">Low Stock Items</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card info">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-3x mb-3"></i>
                <h3><?php echo $stats['today_sales_count']; ?></h3>
                <p class="mb-0">Today's Sales</p>
                <small><?php echo formatCurrency($stats['today_sales_amount']); ?></small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                <h3><?php echo $stats['month_sales_count']; ?></h3>
                <p class="mb-0">This Month</p>
                <small><?php echo formatCurrency($stats['month_sales_amount']); ?></small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Sales -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-receipt"></i> Recent Sales
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recent_sales)): ?>
                    <p class="text-muted text-center">No sales found.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Cashier</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_sales as $sale): ?>
                                    <tr>
                                        <td><?php echo $sale['invoice_no']; ?></td>
                                        <td><?php echo $sale['customer_name'] ?: 'Walk-in Customer'; ?></td>
                                        <td><?php echo formatCurrency($sale['total_amount']); ?></td>
                                        <td><?php echo $sale['cashier_name']; ?></td>
                                        <td><?php echo formatDateTime($sale['sale_date']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center">
                        <a href="reports.php" class="btn btn-primary">
                            <i class="fas fa-chart-bar"></i> View All Reports
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Low Stock Alert -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Low Stock Alert
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($low_stock_products)): ?>
                    <p class="text-muted text-center">All products are well stocked!</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($low_stock_products as $product): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?php echo $product['name']; ?></strong><br>
                                    <small class="text-muted"><?php echo $product['barcode']; ?></small>
                                </div>
                                <span class="badge bg-danger"><?php echo $product['stock']; ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <a href="products.php" class="btn btn-warning">
                            <i class="fas fa-box"></i> Manage Products
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="products.php" class="btn btn-outline-primary btn-lg w-100 dashboard-card">
                            <i class="fas fa-box fa-2x mb-2"></i><br>
                            Manage Products
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="../billing/index.php" class="btn btn-outline-success btn-lg w-100 dashboard-card">
                            <i class="fas fa-cash-register fa-2x mb-2"></i><br>
                            Start Billing
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="reports.php" class="btn btn-outline-info btn-lg w-100 dashboard-card">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                            View Reports
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="#" class="btn btn-outline-secondary btn-lg w-100 dashboard-card" data-bs-toggle="modal" data-bs-target="#settingsModal">
                            <i class="fas fa-cog fa-2x mb-2"></i><br>
                            Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
