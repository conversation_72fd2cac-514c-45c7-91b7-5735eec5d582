<?php
// Database setup script
require_once 'config/database.php';

$page_title = 'Database Setup';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Supermarket POS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-database"></i> Database Setup</h4>
                    </div>
                    <div class="card-body">
                        <?php
                        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                            try {
                                // Create database connection without specifying database
                                $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
                                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                                
                                echo '<div class="alert alert-info">Setting up database...</div>';
                                
                                // Read and execute schema
                                $schema = file_get_contents('database/schema.sql');
                                $statements = explode(';', $schema);
                                
                                foreach ($statements as $statement) {
                                    $statement = trim($statement);
                                    if (!empty($statement)) {
                                        $pdo->exec($statement);
                                    }
                                }
                                
                                echo '<div class="alert alert-success">Database schema created successfully!</div>';
                                
                                // Read and execute sample data
                                $sampleData = file_get_contents('database/sample_data.sql');
                                $statements = explode(';', $sampleData);
                                
                                foreach ($statements as $statement) {
                                    $statement = trim($statement);
                                    if (!empty($statement)) {
                                        $pdo->exec($statement);
                                    }
                                }
                                
                                echo '<div class="alert alert-success">Sample data inserted successfully!</div>';
                                echo '<div class="alert alert-warning">
                                        <strong>Default Login Credentials:</strong><br>
                                        Admin: username = <code>admin</code>, password = <code>admin123</code><br>
                                        Cashier: username = <code>cashier</code>, password = <code>cashier123</code>
                                      </div>';
                                echo '<div class="mt-3">
                                        <a href="login.php" class="btn btn-primary">
                                            <i class="fas fa-sign-in-alt"></i> Go to Login
                                        </a>
                                      </div>';
                                
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger">Error: ' . $e->getMessage() . '</div>';
                            }
                        } else {
                            ?>
                            <div class="alert alert-warning">
                                <strong>Important:</strong> This will create the database and insert sample data. 
                                Make sure XAMPP/WAMP is running and MySQL is accessible.
                            </div>
                            
                            <h5>Database Configuration:</h5>
                            <ul class="list-group mb-3">
                                <li class="list-group-item">Host: <?php echo DB_HOST; ?></li>
                                <li class="list-group-item">Database: <?php echo DB_NAME; ?></li>
                                <li class="list-group-item">Username: <?php echo DB_USER; ?></li>
                            </ul>
                            
                            <form method="POST">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-play"></i> Setup Database
                                </button>
                            </form>
                            <?php
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
