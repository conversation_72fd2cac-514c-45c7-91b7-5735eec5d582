<?php
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

requireAdmin();

$pdo = getDBConnection();

// Get date filters
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'sales';

// Sales Report
if ($report_type === 'sales') {
    $stmt = $pdo->prepare("
        SELECT s.*, u.full_name as cashier_name 
        FROM sales s 
        JOIN users u ON s.cashier_id = u.id 
        WHERE DATE(s.sale_date) BETWEEN ? AND ? 
        ORDER BY s.sale_date DESC
    ");
    $stmt->execute([$start_date, $end_date]);
    $sales = $stmt->fetchAll();
    
    // Sales summary
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_sales,
            SUM(total_amount) as total_revenue,
            SUM(tax_amount) as total_tax,
            SUM(discount_amount) as total_discount,
            AVG(total_amount) as avg_sale_amount
        FROM sales 
        WHERE DATE(sale_date) BETWEEN ? AND ?
    ");
    $stmt->execute([$start_date, $end_date]);
    $summary = $stmt->fetch();
}

// Product Sales Report
elseif ($report_type === 'products') {
    $stmt = $pdo->prepare("
        SELECT 
            si.product_name,
            si.product_barcode,
            SUM(si.quantity) as total_quantity,
            SUM(si.line_total) as total_revenue,
            COUNT(DISTINCT si.sale_id) as times_sold,
            AVG(si.unit_price) as avg_price
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        WHERE DATE(s.sale_date) BETWEEN ? AND ?
        GROUP BY si.product_id, si.product_name, si.product_barcode
        ORDER BY total_quantity DESC
    ");
    $stmt->execute([$start_date, $end_date]);
    $product_sales = $stmt->fetchAll();
}

// Daily Sales Report
elseif ($report_type === 'daily') {
    $stmt = $pdo->prepare("
        SELECT 
            DATE(sale_date) as sale_date,
            COUNT(*) as total_sales,
            SUM(total_amount) as total_revenue,
            SUM(tax_amount) as total_tax,
            SUM(discount_amount) as total_discount
        FROM sales 
        WHERE DATE(sale_date) BETWEEN ? AND ?
        GROUP BY DATE(sale_date)
        ORDER BY sale_date DESC
    ");
    $stmt->execute([$start_date, $end_date]);
    $daily_sales = $stmt->fetchAll();
}

// Cashier Performance Report
elseif ($report_type === 'cashiers') {
    $stmt = $pdo->prepare("
        SELECT 
            u.full_name as cashier_name,
            COUNT(s.id) as total_sales,
            SUM(s.total_amount) as total_revenue,
            AVG(s.total_amount) as avg_sale_amount,
            MIN(s.sale_date) as first_sale,
            MAX(s.sale_date) as last_sale
        FROM sales s
        JOIN users u ON s.cashier_id = u.id
        WHERE DATE(s.sale_date) BETWEEN ? AND ?
        GROUP BY s.cashier_id, u.full_name
        ORDER BY total_revenue DESC
    ");
    $stmt->execute([$start_date, $end_date]);
    $cashier_performance = $stmt->fetchAll();
}

$page_title = 'Reports & Analytics';
$extra_js = ['/assets/js/reports.js'];
include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-chart-bar"></i> Reports & Analytics
        </h2>
    </div>
</div>

<!-- Report Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-filter"></i> Report Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="report_type" class="form-label">Report Type</label>
                        <select class="form-select" id="report_type" name="report_type">
                            <option value="sales" <?php echo $report_type === 'sales' ? 'selected' : ''; ?>>Sales Report</option>
                            <option value="products" <?php echo $report_type === 'products' ? 'selected' : ''; ?>>Product Sales</option>
                            <option value="daily" <?php echo $report_type === 'daily' ? 'selected' : ''; ?>>Daily Sales</option>
                            <option value="cashiers" <?php echo $report_type === 'cashiers' ? 'selected' : ''; ?>>Cashier Performance</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="<?php echo $start_date; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="<?php echo $end_date; ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Generate Report
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportReport()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php if ($report_type === 'sales'): ?>
    <!-- Sales Summary -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-receipt fa-2x mb-2"></i>
                    <h4><?php echo $summary['total_sales']; ?></h4>
                    <p class="mb-0">Total Sales</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card info">
                <div class="card-body text-center">
                    <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                    <h4><?php echo formatCurrency($summary['total_revenue']); ?></h4>
                    <p class="mb-0">Total Revenue</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card warning">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x mb-2"></i>
                    <h4><?php echo formatCurrency($summary['total_tax']); ?></h4>
                    <p class="mb-0">Total Tax</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-calculator fa-2x mb-2"></i>
                    <h4><?php echo formatCurrency($summary['avg_sale_amount']); ?></h4>
                    <p class="mb-0">Average Sale</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sales Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Sales Details</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($sales)): ?>
                        <p class="text-center text-muted">No sales found for the selected period.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Invoice</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Tax</th>
                                        <th>Discount</th>
                                        <th>Total</th>
                                        <th>Payment</th>
                                        <th>Cashier</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sales as $sale): ?>
                                        <tr>
                                            <td><code><?php echo $sale['invoice_no']; ?></code></td>
                                            <td><?php echo $sale['customer_name'] ?: 'Walk-in'; ?></td>
                                            <td><?php echo formatCurrency($sale['subtotal']); ?></td>
                                            <td><?php echo formatCurrency($sale['tax_amount']); ?></td>
                                            <td><?php echo formatCurrency($sale['discount_amount']); ?></td>
                                            <td><strong><?php echo formatCurrency($sale['total_amount']); ?></strong></td>
                                            <td>
                                                <span class="badge bg-info"><?php echo ucfirst($sale['payment_method']); ?></span>
                                            </td>
                                            <td><?php echo $sale['cashier_name']; ?></td>
                                            <td><?php echo formatDateTime($sale['sale_date']); ?></td>
                                            <td>
                                                <a href="../billing/print-invoice.php?id=<?php echo $sale['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-info view-details-btn" 
                                                        data-sale-id="<?php echo $sale['id']; ?>">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php elseif ($report_type === 'products'): ?>
    <!-- Product Sales Report -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Product Sales Performance</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($product_sales)): ?>
                        <p class="text-center text-muted">No product sales found for the selected period.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Barcode</th>
                                        <th>Quantity Sold</th>
                                        <th>Revenue</th>
                                        <th>Times Sold</th>
                                        <th>Avg Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($product_sales as $product): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($product['product_name']); ?></td>
                                            <td><code><?php echo $product['product_barcode']; ?></code></td>
                                            <td><strong><?php echo $product['total_quantity']; ?></strong></td>
                                            <td><?php echo formatCurrency($product['total_revenue']); ?></td>
                                            <td><?php echo $product['times_sold']; ?></td>
                                            <td><?php echo formatCurrency($product['avg_price']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

<?php endif; ?>

<?php include '../includes/footer.php'; ?>
