<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

requireLogin();

header('Content-Type: application/json');

$pdo = getDBConnection();

$query = isset($_GET['q']) ? trim($_GET['q']) : '';
$isBarcode = isset($_GET['barcode']) && $_GET['barcode'] == '1';

if (empty($query)) {
    echo json_encode(['success' => false, 'message' => 'Query is required']);
    exit;
}

try {
    if ($isBarcode) {
        // Exact barcode search
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name, b.name as brand_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN brands b ON p.brand_id = b.id 
            WHERE p.barcode = ? AND p.is_active = 1
        ");
        $stmt->execute([$query]);
    } else {
        // Name search with LIKE
        $searchTerm = "%$query%";
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name, b.name as brand_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN brands b ON p.brand_id = b.id 
            WHERE (p.name LIKE ? OR p.barcode LIKE ?) AND p.is_active = 1 
            ORDER BY p.name ASC 
            LIMIT 10
        ");
        $stmt->execute([$searchTerm, $searchTerm]);
    }
    
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format products for response
    $formattedProducts = [];
    foreach ($products as $product) {
        $formattedProducts[] = [
            'id' => $product['id'],
            'name' => $product['name'],
            'barcode' => $product['barcode'],
            'price' => floatval($product['price']),
            'stock' => intval($product['stock']),
            'min_stock' => intval($product['min_stock']),
            'tax_percent' => floatval($product['tax_percent']),
            'discount_percent' => floatval($product['discount_percent']),
            'category_name' => $product['category_name'],
            'brand_name' => $product['brand_name']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'products' => $formattedProducts,
        'count' => count($formattedProducts)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
