<?php
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

requireAdmin();

$pdo = getDBConnection();
$message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_product':
                $name = sanitizeInput($_POST['name']);
                $barcode = sanitizeInput($_POST['barcode']);
                $category_id = $_POST['category_id'] ?: null;
                $brand_id = $_POST['brand_id'] ?: null;
                $price = floatval($_POST['price']);
                $cost_price = floatval($_POST['cost_price']);
                $stock = intval($_POST['stock']);
                $min_stock = intval($_POST['min_stock']);
                $tax_percent = floatval($_POST['tax_percent']);
                $discount_percent = floatval($_POST['discount_percent']);
                $description = sanitizeInput($_POST['description']);
                
                // Generate barcode if empty
                if (empty($barcode)) {
                    $barcode = generateBarcode();
                }
                
                try {
                    $stmt = $pdo->prepare("INSERT INTO products (name, barcode, category_id, brand_id, price, cost_price, stock, min_stock, tax_percent, discount_percent, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$name, $barcode, $category_id, $brand_id, $price, $cost_price, $stock, $min_stock, $tax_percent, $discount_percent, $description]);
                    $message = showAlert('Product added successfully!', 'success');
                } catch (Exception $e) {
                    $message = showAlert('Error adding product: ' . $e->getMessage(), 'danger');
                }
                break;
                
            case 'edit_product':
                $id = intval($_POST['id']);
                $name = sanitizeInput($_POST['name']);
                $barcode = sanitizeInput($_POST['barcode']);
                $category_id = $_POST['category_id'] ?: null;
                $brand_id = $_POST['brand_id'] ?: null;
                $price = floatval($_POST['price']);
                $cost_price = floatval($_POST['cost_price']);
                $stock = intval($_POST['stock']);
                $min_stock = intval($_POST['min_stock']);
                $tax_percent = floatval($_POST['tax_percent']);
                $discount_percent = floatval($_POST['discount_percent']);
                $description = sanitizeInput($_POST['description']);
                
                try {
                    $stmt = $pdo->prepare("UPDATE products SET name = ?, barcode = ?, category_id = ?, brand_id = ?, price = ?, cost_price = ?, stock = ?, min_stock = ?, tax_percent = ?, discount_percent = ?, description = ? WHERE id = ?");
                    $stmt->execute([$name, $barcode, $category_id, $brand_id, $price, $cost_price, $stock, $min_stock, $tax_percent, $discount_percent, $description, $id]);
                    $message = showAlert('Product updated successfully!', 'success');
                } catch (Exception $e) {
                    $message = showAlert('Error updating product: ' . $e->getMessage(), 'danger');
                }
                break;
                
            case 'delete_product':
                $id = intval($_POST['id']);
                try {
                    $stmt = $pdo->prepare("UPDATE products SET is_active = 0 WHERE id = ?");
                    $stmt->execute([$id]);
                    $message = showAlert('Product deleted successfully!', 'success');
                } catch (Exception $e) {
                    $message = showAlert('Error deleting product: ' . $e->getMessage(), 'danger');
                }
                break;
                
            case 'add_category':
                $name = sanitizeInput($_POST['category_name']);
                $description = sanitizeInput($_POST['category_description']);
                
                try {
                    $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
                    $stmt->execute([$name, $description]);
                    $message = showAlert('Category added successfully!', 'success');
                } catch (Exception $e) {
                    $message = showAlert('Error adding category: ' . $e->getMessage(), 'danger');
                }
                break;
                
            case 'add_brand':
                $name = sanitizeInput($_POST['brand_name']);
                $description = sanitizeInput($_POST['brand_description']);
                
                try {
                    $stmt = $pdo->prepare("INSERT INTO brands (name, description) VALUES (?, ?)");
                    $stmt->execute([$name, $description]);
                    $message = showAlert('Brand added successfully!', 'success');
                } catch (Exception $e) {
                    $message = showAlert('Error adding brand: ' . $e->getMessage(), 'danger');
                }
                break;
        }
    }
}

// Get search and filter parameters
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$category_filter = isset($_GET['category']) ? intval($_GET['category']) : '';
$brand_filter = isset($_GET['brand']) ? intval($_GET['brand']) : '';
$stock_filter = isset($_GET['stock']) ? $_GET['stock'] : '';

// Build query
$where_conditions = ['p.is_active = 1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = '(p.name LIKE ? OR p.barcode LIKE ?)';
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category_filter)) {
    $where_conditions[] = 'p.category_id = ?';
    $params[] = $category_filter;
}

if (!empty($brand_filter)) {
    $where_conditions[] = 'p.brand_id = ?';
    $params[] = $brand_filter;
}

if ($stock_filter === 'low') {
    $where_conditions[] = 'p.stock <= p.min_stock';
} elseif ($stock_filter === 'out') {
    $where_conditions[] = 'p.stock = 0';
}

$where_clause = implode(' AND ', $where_conditions);

// Get products
$stmt = $pdo->prepare("SELECT p.*, c.name as category_name, b.name as brand_name FROM products p LEFT JOIN categories c ON p.category_id = c.id LEFT JOIN brands b ON p.brand_id = b.id WHERE $where_clause ORDER BY p.name");
$stmt->execute($params);
$products = $stmt->fetchAll();

// Get categories and brands for dropdowns
$categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll();
$brands = $pdo->query("SELECT * FROM brands ORDER BY name")->fetchAll();

$page_title = 'Product Management';
$extra_js = ['/assets/js/products.js'];
include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-box"></i> Product Management
        </h2>
    </div>
</div>

<?php echo $message; ?>

<!-- Action Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="fas fa-plus"></i> Add Product
                        </button>
                        <button class="btn btn-secondary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                            <i class="fas fa-tags"></i> Add Category
                        </button>
                        <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addBrandModal">
                            <i class="fas fa-trademark"></i> Add Brand
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-search"></i> Search & Filter</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="Product name or barcode" value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"
                                        <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="brand" class="form-label">Brand</label>
                        <select class="form-select" id="brand" name="brand">
                            <option value="">All Brands</option>
                            <?php foreach ($brands as $brand): ?>
                                <option value="<?php echo $brand['id']; ?>"
                                        <?php echo $brand_filter == $brand['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($brand['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="stock" class="form-label">Stock Status</label>
                        <select class="form-select" id="stock" name="stock">
                            <option value="">All Products</option>
                            <option value="low" <?php echo $stock_filter === 'low' ? 'selected' : ''; ?>>Low Stock</option>
                            <option value="out" <?php echo $stock_filter === 'out' ? 'selected' : ''; ?>>Out of Stock</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <a href="products.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Products Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Products (<?php echo count($products); ?> items)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($products)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No products found.</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="fas fa-plus"></i> Add First Product
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover table-custom">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Barcode</th>
                                    <th>Category</th>
                                    <th>Brand</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                            <?php if ($product['description']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($product['description']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><code><?php echo $product['barcode']; ?></code></td>
                                        <td><?php echo $product['category_name'] ?: '-'; ?></td>
                                        <td><?php echo $product['brand_name'] ?: '-'; ?></td>
                                        <td><?php echo formatCurrency($product['price']); ?></td>
                                        <td>
                                            <span class="badge <?php echo isLowStock($product['stock'], $product['min_stock']) ? 'bg-danger' : ($product['stock'] <= $product['min_stock'] * 2 ? 'bg-warning' : 'bg-success'); ?>">
                                                <?php echo $product['stock']; ?>
                                            </span>
                                            <small class="text-muted d-block">Min: <?php echo $product['min_stock']; ?></small>
                                        </td>
                                        <td>
                                            <?php if ($product['stock'] == 0): ?>
                                                <span class="badge bg-danger">Out of Stock</span>
                                            <?php elseif (isLowStock($product['stock'], $product['min_stock'])): ?>
                                                <span class="badge bg-warning">Low Stock</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">In Stock</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary edit-product-btn"
                                                        data-product='<?php echo json_encode($product); ?>'
                                                        data-bs-toggle="modal" data-bs-target="#editProductModal">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger delete-product-btn"
                                                        data-id="<?php echo $product['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($product['name']); ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add New Product</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="addProductForm">
                <input type="hidden" name="action" value="add_product">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_name" class="form-label">Product Name *</label>
                                <input type="text" class="form-control" id="add_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_barcode" class="form-label">Barcode</label>
                                <input type="text" class="form-control" id="add_barcode" name="barcode"
                                       placeholder="Leave empty to auto-generate">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_category_id" class="form-label">Category</label>
                                <select class="form-select" id="add_category_id" name="category_id">
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>">
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_brand_id" class="form-label">Brand</label>
                                <select class="form-select" id="add_brand_id" name="brand_id">
                                    <option value="">Select Brand</option>
                                    <?php foreach ($brands as $brand): ?>
                                        <option value="<?php echo $brand['id']; ?>">
                                            <?php echo htmlspecialchars($brand['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_price" class="form-label">Selling Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="add_price" name="price"
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_cost_price" class="form-label">Cost Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="add_cost_price" name="cost_price"
                                           step="0.01" min="0" value="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_stock" class="form-label">Stock Quantity *</label>
                                <input type="number" class="form-control" id="add_stock" name="stock"
                                       min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_min_stock" class="form-label">Minimum Stock</label>
                                <input type="number" class="form-control" id="add_min_stock" name="min_stock"
                                       min="0" value="10">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_tax_percent" class="form-label">Tax %</label>
                                <input type="number" class="form-control" id="add_tax_percent" name="tax_percent"
                                       step="0.01" min="0" max="100" value="18">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_discount_percent" class="form-label">Discount %</label>
                                <input type="number" class="form-control" id="add_discount_percent" name="discount_percent"
                                       step="0.01" min="0" max="100" value="0">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="add_description" class="form-label">Description</label>
                        <textarea class="form-control" id="add_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Add Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Product Modal -->
<div class="modal fade" id="editProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit"></i> Edit Product</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editProductForm">
                <input type="hidden" name="action" value="edit_product">
                <input type="hidden" name="id" id="edit_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_name" class="form-label">Product Name *</label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_barcode" class="form-label">Barcode</label>
                                <input type="text" class="form-control" id="edit_barcode" name="barcode">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_category_id" class="form-label">Category</label>
                                <select class="form-select" id="edit_category_id" name="category_id">
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>">
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_brand_id" class="form-label">Brand</label>
                                <select class="form-select" id="edit_brand_id" name="brand_id">
                                    <option value="">Select Brand</option>
                                    <?php foreach ($brands as $brand): ?>
                                        <option value="<?php echo $brand['id']; ?>">
                                            <?php echo htmlspecialchars($brand['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_price" class="form-label">Selling Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="edit_price" name="price"
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_cost_price" class="form-label">Cost Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="edit_cost_price" name="cost_price"
                                           step="0.01" min="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_stock" class="form-label">Stock Quantity *</label>
                                <input type="number" class="form-control" id="edit_stock" name="stock"
                                       min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_min_stock" class="form-label">Minimum Stock</label>
                                <input type="number" class="form-control" id="edit_min_stock" name="min_stock"
                                       min="0">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_tax_percent" class="form-label">Tax %</label>
                                <input type="number" class="form-control" id="edit_tax_percent" name="tax_percent"
                                       step="0.01" min="0" max="100">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_discount_percent" class="form-label">Discount %</label>
                                <input type="number" class="form-control" id="edit_discount_percent" name="discount_percent"
                                       step="0.01" min="0" max="100">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-tags"></i> Add New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add_category">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="category_name" class="form-label">Category Name *</label>
                        <input type="text" class="form-control" id="category_name" name="category_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="category_description" class="form-label">Description</label>
                        <textarea class="form-control" id="category_description" name="category_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Add Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Brand Modal -->
<div class="modal fade" id="addBrandModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-trademark"></i> Add New Brand</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add_brand">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="brand_name" class="form-label">Brand Name *</label>
                        <input type="text" class="form-control" id="brand_name" name="brand_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="brand_description" class="form-label">Description</label>
                        <textarea class="form-control" id="brand_description" name="brand_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Add Brand
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
