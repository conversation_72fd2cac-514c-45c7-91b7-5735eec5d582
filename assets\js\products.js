// Products management JavaScript

$(document).ready(function() {
    // Edit product button click
    $('.edit-product-btn').on('click', function() {
        const product = $(this).data('product');
        
        // Populate edit form
        $('#edit_id').val(product.id);
        $('#edit_name').val(product.name);
        $('#edit_barcode').val(product.barcode);
        $('#edit_category_id').val(product.category_id || '');
        $('#edit_brand_id').val(product.brand_id || '');
        $('#edit_price').val(product.price);
        $('#edit_cost_price').val(product.cost_price);
        $('#edit_stock').val(product.stock);
        $('#edit_min_stock').val(product.min_stock);
        $('#edit_tax_percent').val(product.tax_percent);
        $('#edit_discount_percent').val(product.discount_percent);
        $('#edit_description').val(product.description || '');
    });
    
    // Delete product button click
    $('.delete-product-btn').on('click', function() {
        const productId = $(this).data('id');
        const productName = $(this).data('name');
        
        if (confirm(`Are you sure you want to delete "${productName}"?`)) {
            // Create and submit delete form
            const form = $('<form>', {
                method: 'POST',
                style: 'display: none;'
            });
            
            form.append($('<input>', {
                type: 'hidden',
                name: 'action',
                value: 'delete_product'
            }));
            
            form.append($('<input>', {
                type: 'hidden',
                name: 'id',
                value: productId
            }));
            
            $('body').append(form);
            form.submit();
        }
    });
    
    // Form validation
    $('#addProductForm, #editProductForm').on('submit', function(e) {
        const form = $(this);
        let isValid = true;
        
        // Validate required fields
        form.find('input[required], select[required]').each(function() {
            const field = $(this);
            if (!field.val().trim()) {
                field.addClass('is-invalid');
                isValid = false;
            } else {
                field.removeClass('is-invalid');
            }
        });
        
        // Validate price
        const price = form.find('input[name="price"]').val();
        if (price && (isNaN(price) || parseFloat(price) <= 0)) {
            form.find('input[name="price"]').addClass('is-invalid');
            isValid = false;
        }
        
        // Validate stock
        const stock = form.find('input[name="stock"]').val();
        if (stock && (isNaN(stock) || parseInt(stock) < 0)) {
            form.find('input[name="stock"]').addClass('is-invalid');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            showAlert('Please fill in all required fields correctly.', 'danger');
        }
    });
    
    // Clear form validation on input
    $('input, select, textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
    });
    
    // Auto-generate barcode
    $('#generateBarcode').on('click', function() {
        const barcode = Math.random().toString().substr(2, 12);
        $('#add_barcode').val(barcode);
    });
    
    // Calculate profit margin
    $('input[name="cost_price"], input[name="price"]').on('input', function() {
        const form = $(this).closest('form');
        const costPrice = parseFloat(form.find('input[name="cost_price"]').val()) || 0;
        const sellingPrice = parseFloat(form.find('input[name="price"]').val()) || 0;
        
        if (costPrice > 0 && sellingPrice > 0) {
            const profit = sellingPrice - costPrice;
            const margin = (profit / costPrice) * 100;
            
            let marginText = `Profit: ₹${profit.toFixed(2)} (${margin.toFixed(1)}%)`;
            if (margin < 0) {
                marginText = `<span class="text-danger">${marginText}</span>`;
            } else if (margin < 10) {
                marginText = `<span class="text-warning">${marginText}</span>`;
            } else {
                marginText = `<span class="text-success">${marginText}</span>`;
            }
            
            form.find('.profit-margin').html(marginText);
        } else {
            form.find('.profit-margin').html('');
        }
    });
    
    // Search functionality
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = $(this).val();
        
        searchTimeout = setTimeout(function() {
            if (searchTerm.length >= 3 || searchTerm.length === 0) {
                // Auto-submit search form
                $('form').first().submit();
            }
        }, 500);
    });
    
    // Quick stock update
    $('.stock-input').on('change', function() {
        const productId = $(this).data('product-id');
        const newStock = $(this).val();
        
        if (confirm('Update stock quantity?')) {
            // AJAX call to update stock
            $.ajax({
                url: 'ajax/update_stock.php',
                method: 'POST',
                data: {
                    product_id: productId,
                    stock: newStock
                },
                success: function(response) {
                    if (response.success) {
                        showAlert('Stock updated successfully!', 'success');
                        location.reload();
                    } else {
                        showAlert(response.message || 'Error updating stock', 'danger');
                    }
                },
                error: function() {
                    showAlert('Network error occurred', 'danger');
                }
            });
        }
    });
});

// Barcode scanner integration (if available)
function handleBarcodeScanned(barcode) {
    // Fill barcode field if modal is open
    if ($('#addProductModal').hasClass('show')) {
        $('#add_barcode').val(barcode);
    } else if ($('#editProductModal').hasClass('show')) {
        $('#edit_barcode').val(barcode);
    } else {
        // Search for product
        $('#search').val(barcode);
        $('form').first().submit();
    }
}

// Export functions
function exportProducts() {
    window.location.href = 'export.php?type=products';
}

function importProducts() {
    $('#importModal').modal('show');
}
