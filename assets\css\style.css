/* Custom styles for Supermarket POS */

/* General Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
}

/* Login Page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    width: 100%;
    max-width: 400px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
}

.login-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 2rem;
    text-align: center;
}

.login-body {
    padding: 2rem;
}

/* Dashboard Cards */
.dashboard-card {
    transition: transform 0.3s ease;
    cursor: pointer;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.stat-card {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.stat-card.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.stat-card.danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.stat-card.info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

/* Product Management */
.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 5px;
}

.stock-badge {
    font-size: 0.8em;
}

.low-stock {
    background-color: #dc3545 !important;
}

.medium-stock {
    background-color: #ffc107 !important;
}

.high-stock {
    background-color: #28a745 !important;
}

/* Billing Interface */
.billing-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.cart-item {
    border-bottom: 1px solid #eee;
    padding: 1rem 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-total {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 5px 5px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
}

.search-result-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Invoice/Receipt Styles */
.invoice-container {
    width: 300px;
    margin: 0 auto;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    background: white;
    padding: 20px;
}

.invoice-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px dashed #000;
    padding-bottom: 10px;
}

.invoice-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.invoice-info {
    margin-bottom: 15px;
    border-bottom: 1px dashed #000;
    padding-bottom: 10px;
}

.invoice-items {
    margin-bottom: 15px;
}

.invoice-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.invoice-total {
    border-top: 2px dashed #000;
    padding-top: 10px;
    margin-top: 10px;
}

.invoice-footer {
    text-align: center;
    margin-top: 20px;
    border-top: 1px dashed #000;
    padding-top: 10px;
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }
    
    .invoice-container,
    .invoice-container * {
        visibility: visible;
    }
    
    .invoice-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        margin: 0;
        padding: 0;
    }
    
    .no-print {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .billing-container {
        padding: 1rem;
    }
    
    .invoice-container {
        width: 100%;
        max-width: 300px;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

/* Custom Buttons */
.btn-custom {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Table Styles */
.table-custom {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.table-custom thead {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

/* Form Styles */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group-text {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
}
